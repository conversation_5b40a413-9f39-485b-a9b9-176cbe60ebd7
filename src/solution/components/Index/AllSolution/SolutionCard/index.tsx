import React, { useState, useRef, useEffect } from 'react';
import { map } from 'lodash';
import { FormattedMessage } from 'react-intl';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { getEnv, ENV } from '@/help-fe-common/utils/global/env';
import { solutionBasePath } from '@/solution/constants';
import styles from './index.module.scss';

interface IProps {
  cardItemData: any;
  setSearchValue: (value: string) => void;
  scrollIntoTop: () => void;
}

// 自定义Balloon组件，解决焦点滚动问题
interface CustomBalloonProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  style?: React.CSSProperties;
  align?: string;
}

const CustomBalloon: React.FC<CustomBalloonProps> = ({ trigger, children, style, align = 't' }) => {
  const [visible, setVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const balloonRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const calculatePosition = () => {
    if (!triggerRef.current || !balloonRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const balloonRect = balloonRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let top = 0;
    let left = 0;

    // 根据align参数计算位置
    switch (align) {
      case 't': // 上方
        top = triggerRect.top - balloonRect.height - 8;
        left = triggerRect.left + (triggerRect.width - balloonRect.width) / 2;
        break;
      case 'b': // 下方
        top = triggerRect.bottom + 8;
        left = triggerRect.left + (triggerRect.width - balloonRect.width) / 2;
        break;
      case 'l': // 左侧
        top = triggerRect.top + (triggerRect.height - balloonRect.height) / 2;
        left = triggerRect.left - balloonRect.width - 8;
        break;
      case 'r': // 右侧
        top = triggerRect.top + (triggerRect.height - balloonRect.height) / 2;
        left = triggerRect.right + 8;
        break;
      default:
        top = triggerRect.top - balloonRect.height - 8;
        left = triggerRect.left + (triggerRect.width - balloonRect.width) / 2;
    }

    // 边界检测，防止超出视口
    if (left < 8) left = 8;
    if (left + balloonRect.width > viewportWidth - 8) {
      left = viewportWidth - balloonRect.width - 8;
    }
    if (top < 8) top = 8;
    if (top + balloonRect.height > viewportHeight - 8) {
      top = viewportHeight - balloonRect.height - 8;
    }

    setPosition({ top, left });
  };

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setVisible(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setVisible(false);
    }, 100);
  };

  useEffect(() => {
    if (visible) {
      calculatePosition();
    }
  }, [visible]);

  useEffect(() => {
    const handleResize = () => {
      if (visible) {
        calculatePosition();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [visible]);

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={{ display: 'inline-block' }}
      >
        {trigger}
      </div>
      {visible && (
        <div
          ref={balloonRef}
          className={styles.customBalloon}
          style={{
            position: 'fixed',
            top: position.top,
            left: position.left,
            zIndex: 1000,
            ...style,
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div className={styles.balloonContent}>
            {children}
          </div>
          <div className={`${styles.balloonArrow} ${styles[`balloonArrow${align.toUpperCase()}`]}`} />
        </div>
      )}
    </>
  );
};

const SolutionCard = ({ cardItemData, setSearchValue, scrollIntoTop }: IProps) => {
  const { name, url, shortDesc, categoryTagMap, freeInfo } = cardItemData;

  /**
   * 根据环境生成 URL
   * @param url
   * @returns 生成的完整 URL
   */
  const generateUrlByEnv = (path: string): string => {
    // 获取当前环境
    const env = getEnv();

    // 预发环境：改写路径并添加查询参数
    if (env === ENV.PRE) {
      const queryString = 'closeRedirect=true';

      const solutionCode = path?.split('/').pop();

      // 拼接预发环境的 URL
      return `${solutionBasePath}/${solutionCode}?${queryString}`;
    }

    return path;
  };

  const tagTarget = (
    <div className={styles.freeTag} >
      <span className={styles.logo}>
        <i className="help-iconfont help-icon-free-logo" />
      </span>
      <span className={styles.text}>免费试用</span>
    </div>
  );

  return (
    <div className={styles.solutionCard}>
      <a
        href={generateUrlByEnv(url)}
        onClick={() => {
          sendSlsLog({ page: 'solutionIndex', section: 'solutionCard', action: 'click', userParams1: name, userParams2: url });
        }}
        title={name}
        target="_blank"
        rel="noreferrer"
      >
        <div className={styles.head}>
          <h5 className={styles.title} title={name}>{name}</h5>
        </div>
        <div className={styles.content}>
          <p className={styles.desc} title={shortDesc}>{shortDesc}</p>
          <div className={styles.product}>
            {map(categoryTagMap?.product_name, (productName, pipCode) => {
              return (
                <div className={styles.productItem}>
                  <i className={`dbl-icon-product-${pipCode} dbl-icon-product-aliyun`} />
                  <span>{productName}</span>
                </div>
              );
            })}
          </div>
          <div className={styles.tags}>
            {map(categoryTagMap?.tech_solution_scene, (tagName) => (
              <span
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  setSearchValue(tagName);
                  scrollIntoTop();
                  sendSlsLog({
                    page: 'solutionIndex',
                    section: 'solutionCard',
                    action: 'searchClick',
                    userParams1: tagName,
                    userParam2: name,
                  });
                }}
              >
                {tagName}
              </span>
            ))}
          </div>
          <div className={styles.cardBottom}>
            <div className={styles.cardBottomLeft}>
              <span>查看详情</span>
              <i className="help-iconfont help-icon-small-arrow" />
            </div>
            <div className={styles.cardBottomRight}>
              {
                freeInfo?.isFree &&
                <CustomBalloon
                  style={{ minWidth: 240, height: 72, padding: 12 }}
                  trigger={tagTarget}
                  align="t"
                >
                  <div className={styles.freeTip}>
                    <p className={styles.logo}>
                      <span className={styles.token}>{`${freeInfo?.freeToken} 试用点`}</span>
                      <span> 起/小时免费试用该方案</span>
                    </p>
                    <div className={styles.linkContainer}>
                      {freeInfo?.deployUrl && (
                        <>
                          <a
                            href={freeInfo.deployUrl}
                            className={styles.link}
                            target="_blank"
                            rel="noreferrer"
                            onClick={(e) => {
                              e.stopPropagation();
                              sendSlsLog({
                                page: 'solutionIndex',
                                section: 'solutionCard',
                                action: 'tryNow',
                                userParams1: name,
                                userParams2: freeInfo.deployUrl,
                              });
                            }}
                          ><FormattedMessage id="help.solution.tryNow" />
                            <span><i className="help-iconfont help-icon-Right-Arrow" /></span>
                          </a>
                          <span className={styles.divider}>|</span>
                        </>
                      )}
                      <div className={styles.freeGetContainer}>
                        <span className={styles.noPointsText}>没有试用点？</span>
                        <a
                          href={getEnv() === ENV.PRE ? 'https://pre-www.aliyun.com/solution/free' : 'https://www.aliyun.com/solution/free'}
                          className={styles.freeGetLink}
                          target="_blank"
                          rel="noreferrer"
                          onClick={(e) => e.stopPropagation()}
                        >免费领取
                        </a>
                      </div>
                    </div>
                  </div>
                </CustomBalloon>
              }
            </div>
          </div>
        </div>
      </a>
    </div>
  );
};

export default SolutionCard;
