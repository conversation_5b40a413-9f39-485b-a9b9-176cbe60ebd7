# CustomBalloon 组件

自定义的气泡提示组件，用于解决 @alifd/next 的 Balloon 组件在 hover 触发时导致页面滚动到鼠标焦点位置的问题。

## 特性

- ✅ 解决焦点滚动问题：鼠标移出时不会导致页面滚动
- ✅ 支持多种触发方式：hover（默认）和 click
- ✅ 支持多种对齐方式：上(t)、下(b)、左(l)、右(r)
- ✅ 自动边界检测：防止气泡超出视口边界
- ✅ 支持受控和非受控模式
- ✅ 响应式设计：自动适应窗口大小变化和滚动
- ✅ 无边框圆角设计：符合用户偏好

## API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| trigger | 触发元素 | ReactNode | - | ✅ |
| children | 气泡内容 | ReactNode | - | ✅ |
| style | 气泡样式 | CSSProperties | - | ❌ |
| align | 对齐方式 | 't' \| 'b' \| 'l' \| 'r' | 't' | ❌ |
| visible | 受控模式下的显示状态 | boolean | - | ❌ |
| onVisibleChange | 显示状态变化回调 | (visible: boolean) => void | - | ❌ |
| triggerType | 触发方式 | 'hover' \| 'click' | 'hover' | ❌ |
| offset | 位置偏移 | [number, number] | [0, 0] | ❌ |
| disabled | 是否禁用 | boolean | false | ❌ |

## 使用示例

### 基础用法（hover 触发）

```tsx
import CustomBalloon from '@/solution/components/Common/CustomBalloon';

<CustomBalloon
  trigger={<button>悬停显示</button>}
  align="t"
>
  <div>这是气泡内容</div>
</CustomBalloon>
```

### 点击触发

```tsx
<CustomBalloon
  trigger={<button>点击显示</button>}
  triggerType="click"
  align="b"
>
  <div>点击触发的气泡内容</div>
</CustomBalloon>
```

### 受控模式

```tsx
const [visible, setVisible] = useState(false);

<CustomBalloon
  trigger={<button onClick={() => setVisible(!visible)}>受控模式</button>}
  visible={visible}
  onVisibleChange={setVisible}
  triggerType="click"
>
  <div>受控模式的气泡内容</div>
</CustomBalloon>
```

### 自定义样式和偏移

```tsx
<CustomBalloon
  trigger={<span>自定义样式</span>}
  style={{ minWidth: 200, padding: 16, backgroundColor: '#f0f0f0' }}
  offset={[10, -5]}
  align="r"
>
  <div>自定义样式的气泡</div>
</CustomBalloon>
```

## 解决的问题

1. **焦点滚动问题**：原 @alifd/next Balloon 组件在 hover 状态下，当 checkbox、pagination 等组件处于选中状态时，鼠标移出会导致页面滚动至鼠标焦点位置
2. **边界检测**：自动检测视口边界，防止气泡超出屏幕范围
3. **响应式适配**：监听窗口大小变化和页面滚动，自动调整气泡位置

## 注意事项

- 气泡使用 `position: absolute` 定位，确保父容器有合适的定位上下文
- 在 hover 模式下，鼠标移出触发元素或气泡内容都会关闭气泡
- 在 click 模式下，点击外部区域会关闭气泡
- 组件会自动清理事件监听器和定时器，避免内存泄漏
