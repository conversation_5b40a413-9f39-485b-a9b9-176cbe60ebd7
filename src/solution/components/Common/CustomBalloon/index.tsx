import React, { useState, useRef, useEffect } from 'react';
import styles from './index.module.scss';

interface CustomBalloonProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  style?: React.CSSProperties;
  align?: 't' | 'b' | 'l' | 'r';
  visible?: boolean;
  onVisibleChange?: (visible: boolean) => void;
  triggerType?: 'hover' | 'click';
  offset?: [number, number];
  disabled?: boolean;
}

const CustomBalloon: React.FC<CustomBalloonProps> = ({
  trigger,
  children,
  style,
  align = 't',
  visible: controlledVisible,
  onVisibleChange,
  triggerType = 'hover',
  offset = [0, 0],
  disabled = false,
}) => {
  const [internalVisible, setInternalVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const balloonRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 使用受控或非受控模式
  const visible = controlledVisible !== undefined ? controlledVisible : internalVisible;

  const calculatePosition = () => {
    if (!triggerRef.current || !balloonRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const balloonRect = balloonRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    let top = 0;
    let left = 0;

    // 根据align参数计算位置
    switch (align) {
      case 't': // 上方
        top = triggerRect.top + scrollTop - balloonRect.height - 8 + offset[1];
        left = triggerRect.left + scrollLeft + (triggerRect.width - balloonRect.width) / 2 + offset[0];
        break;
      case 'b': // 下方
        top = triggerRect.bottom + scrollTop + 8 + offset[1];
        left = triggerRect.left + scrollLeft + (triggerRect.width - balloonRect.width) / 2 + offset[0];
        break;
      case 'l': // 左侧
        top = triggerRect.top + scrollTop + (triggerRect.height - balloonRect.height) / 2 + offset[1];
        left = triggerRect.left + scrollLeft - balloonRect.width - 8 + offset[0];
        break;
      case 'r': // 右侧
        top = triggerRect.top + scrollTop + (triggerRect.height - balloonRect.height) / 2 + offset[1];
        left = triggerRect.right + scrollLeft + 8 + offset[0];
        break;
      default:
        top = triggerRect.top + scrollTop - balloonRect.height - 8 + offset[1];
        left = triggerRect.left + scrollLeft + (triggerRect.width - balloonRect.width) / 2 + offset[0];
    }

    // 边界检测，防止超出视口
    if (left < 8) left = 8;
    if (left + balloonRect.width > viewportWidth - 8) {
      left = viewportWidth - balloonRect.width - 8;
    }
    if (top < scrollTop + 8) top = scrollTop + 8;
    if (top + balloonRect.height > scrollTop + viewportHeight - 8) {
      top = scrollTop + viewportHeight - balloonRect.height - 8;
    }

    setPosition({ top, left });
  };

  const updateVisible = (newVisible: boolean) => {
    if (disabled) return;

    if (controlledVisible === undefined) {
      setInternalVisible(newVisible);
    }
    onVisibleChange?.(newVisible);
  };

  const handleMouseEnter = () => {
    if (triggerType !== 'hover') return;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    updateVisible(true);
  };

  const handleMouseLeave = () => {
    if (triggerType !== 'hover') return;

    timeoutRef.current = setTimeout(() => {
      updateVisible(false);
    }, 100);
  };

  const handleClick = (e: React.MouseEvent) => {
    if (triggerType !== 'click') return;

    e.stopPropagation();
    updateVisible(!visible);
  };

  const handleClickOutside = (e: MouseEvent) => {
    if (triggerType !== 'click' || !visible) return;

    const target = e.target as Node;
    if (
      triggerRef.current &&
      balloonRef.current &&
      !triggerRef.current.contains(target) &&
      !balloonRef.current.contains(target)
    ) {
      updateVisible(false);
    }
  };

  useEffect(() => {
    if (visible) {
      // 延迟计算位置，确保DOM已渲染
      const timer = setTimeout(() => {
        calculatePosition();
      }, 0);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [visible, align, offset]);

  useEffect(() => {
    const handleResize = () => {
      if (visible) {
        calculatePosition();
      }
    };

    const handleScroll = () => {
      if (visible) {
        calculatePosition();
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll, true);

    if (triggerType === 'click') {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll, true);
      document.removeEventListener('click', handleClickOutside);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [visible, triggerType]);

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        style={{ display: 'inline-block' }}
      >
        {trigger}
      </div>
      {visible && (
        <div
          ref={balloonRef}
          className={styles.customBalloon}
          style={{
            position: 'absolute',
            top: position.top,
            left: position.left,
            zIndex: 1000,
            ...style,
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div className={styles.balloonContent}>
            {children}
          </div>
          <div className={`${styles.balloonArrow} ${styles[`balloonArrow${align.toUpperCase()}`]}`} />
        </div>
      )}
    </>
  );
};

export default CustomBalloon;
