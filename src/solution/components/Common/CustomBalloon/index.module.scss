// 自定义Balloon组件样式
.customBalloon {
  background: #fff;
  border: 1px solid #E6E6E6;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  position: relative;
  z-index: 1000;

  .balloonContent {
    position: relative;
    z-index: 1;
  }

  .balloonArrow {
    position: absolute;
    width: 0;
    height: 0;
    border: 6px solid transparent;

    &.balloonArrowT {
      bottom: -12px;
      left: 50%;
      transform: translateX(-50%);
      border-top-color: #fff;
      border-bottom: none;

      &::before {
        content: '';
        position: absolute;
        top: -7px;
        left: -6px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-top-color: #E6E6E6;
        border-bottom: none;
      }
    }

    &.balloonArrowB {
      top: -12px;
      left: 50%;
      transform: translateX(-50%);
      border-bottom-color: #fff;
      border-top: none;

      &::before {
        content: '';
        position: absolute;
        bottom: -7px;
        left: -6px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #E6E6E6;
        border-top: none;
      }
    }

    &.balloonArrowL {
      right: -12px;
      top: 50%;
      transform: translateY(-50%);
      border-left-color: #fff;
      border-right: none;

      &::before {
        content: '';
        position: absolute;
        left: -7px;
        top: -6px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-left-color: #E6E6E6;
        border-right: none;
      }
    }

    &.balloonArrowR {
      left: -12px;
      top: 50%;
      transform: translateY(-50%);
      border-right-color: #fff;
      border-left: none;

      &::before {
        content: '';
        position: absolute;
        right: -7px;
        top: -6px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-right-color: #E6E6E6;
        border-left: none;
      }
    }
  }
}
